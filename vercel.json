{"version": 2, "name": "sociality-frontend", "builds": [{"src": "sociality/frontend/package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}], "routes": [{"src": "/api/(.*)", "dest": "https://sociality-backend.onrender.com/api/$1"}, {"src": "/socket.io/(.*)", "dest": "https://sociality-backend.onrender.com/socket.io/$1"}, {"src": "/(.*)", "dest": "/sociality/frontend/dist/$1"}], "buildCommand": "cd sociality/frontend && npm install && npm run build", "outputDirectory": "sociality/frontend/dist", "installCommand": "cd sociality/frontend && npm install", "framework": "vite", "env": {"VITE_API_URL": "https://sociality-backend.onrender.com", "VITE_SOCKET_URL": "https://sociality-backend.onrender.com"}, "functions": {"sociality/frontend/src/**": {"includeFiles": "sociality/frontend/dist/**"}}, "rewrites": [{"source": "/api/(.*)", "destination": "https://sociality-backend.onrender.com/api/$1"}, {"source": "/socket.io/(.*)", "destination": "https://sociality-backend.onrender.com/socket.io/$1"}, {"source": "/(.*)", "destination": "/index.html"}]}