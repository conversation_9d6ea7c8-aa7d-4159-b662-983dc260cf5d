# ================================
# SOCIALITY FRONTEND CONFIGURATION
# ================================

# API Configuration
# For development, use localhost backend
VITE_API_URL=http://localhost:5000
VITE_SOCKET_URL=http://localhost:5000

# Environment
VITE_NODE_ENV=development

# ================================
# PRODUCTION DEPLOYMENT NOTES
# ================================
#
# For production deployment on Vercel:
# 1. Set VITE_API_URL to your Render backend URL
# 2. Set VITE_SOCKET_URL to your Render backend URL
# 3. Set VITE_NODE_ENV=production
#
# Example production values:
# VITE_API_URL=https://your-backend-name.onrender.com
# VITE_SOCKET_URL=https://your-backend-name.onrender.com
# VITE_NODE_ENV=production
#
# Note: All VITE_ prefixed variables are exposed to the browser
# Never put sensitive information in frontend environment variables
