services:
  - type: web
    name: sociality-backend
    env: node
    region: oregon
    plan: free
    buildCommand: cd sociality/backend && npm install
    startCommand: cd sociality/backend && npm start
    healthCheckPath: /api/health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
      - key: MONGO_URI
        fromDatabase:
          name: sociality-db
          property: connectionString
      - key: JWT_SECRET
        generateValue: true
      - key: SESSION_SECRET
        generateValue: true
      - key: CLOUDINARY_CLOUD_NAME
        sync: false
      - key: CLOUDINARY_API_KEY
        sync: false
      - key: CLOUDINARY_API_SECRET
        sync: false
      - key: GOOGLE_CLIENT_ID
        sync: false
      - key: GOOGLE_CLIENT_SECRET
        sync: false
      # Telegram Bot Configuration
      - key: TELEGRAM_BOT_TOKEN
        sync: false
      - key: TELEGRAM_WEBHOOK_URL
        value: https://sociality-backend.onrender.com/api/telegram/webhook

      # Discord Bot Configuration
      - key: DIS<PERSON>RD_BOT_TOKEN
        sync: false
      - key: DISCORD_CLIENT_ID
        sync: false
      - key: DISCORD_CLIENT_SECRET
        sync: false

      # CORS & Frontend Configuration
      - key: FRONTEND_URL
        value: https://sociality-frontend.vercel.app
      - key: CORS_ORIGIN
        value: https://sociality-frontend.vercel.app

      # Federation Configuration
      - key: FEDERATION_ENABLED
        value: true
      - key: FEDERATION_REGISTRY_URL
        value: https://sociality-backend.onrender.com
      - key: PLATFORM_URL
        value: https://sociality-backend.onrender.com
      - key: PLATFORM_NAME
        value: sociality

      # Cross-Platform Features
      - key: ENABLE_CROSS_PLATFORM
        value: true

databases:
  - name: sociality-db
    databaseName: sociality
    user: sociality_user
    region: oregon
    plan: free
