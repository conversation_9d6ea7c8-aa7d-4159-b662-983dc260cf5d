services:
  - type: web
    name: sociality-backend
    env: node
    region: oregon
    plan: free
    buildCommand: cd sociality/backend && npm install
    startCommand: cd sociality/backend && npm start
    healthCheckPath: /api/health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
      - key: MONGO_URI
        fromDatabase:
          name: sociality-db
          property: connectionString
      - key: JWT_SECRET
        generateValue: true
      - key: SESSION_SECRET
        generateValue: true
      - key: CLOUDINARY_CLOUD_NAME
        sync: false
      - key: CLOUDINARY_API_KEY
        sync: false
      - key: CLOUDINARY_API_SECRET
        sync: false
      - key: GOOGLE_CLIENT_ID
        sync: false
      - key: GOO<PERSON>LE_CLIENT_SECRET
        sync: false
      - key: TELEGRAM_BOT_TOKEN
        sync: false
      - key: TELEGRAM_BOT_USERNAME
        sync: false
      - key: DISCORD_BOT_TOKEN
        sync: false
      - key: DISCORD_CLIENT_ID
        sync: false
      - key: FRONTEND_URL
        value: https://sociality-frontend.vercel.app
      - key: FEDERATION_REGISTRY_URL
        value: https://sociality-backend.onrender.com
      - key: PLATFORM_URL
        value: https://sociality-backend.onrender.com
      - key: PLATFORM_NAME
        value: sociality
      - key: FEDERATION_ENABLED
        value: true
      - key: ENABLE_CROSS_PLATFORM
        value: true

databases:
  - name: sociality-db
    databaseName: sociality
    user: sociality_user
    region: oregon
    plan: free
