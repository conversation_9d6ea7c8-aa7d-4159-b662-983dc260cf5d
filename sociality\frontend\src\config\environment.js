/**
 * Environment configuration for different deployment environments
 */

// Get the current environment
const isDevelopment = import.meta.env.DEV;
const isProduction = import.meta.env.PROD;

// API configuration based on environment
export const API_CONFIG = {
  // In development, use Vite proxy (relative URLs)
  // In production, use environment variables or fallback to relative URLs
  BASE_URL: isDevelopment 
    ? '' // Use relative URLs for Vite proxy
    : import.meta.env.VITE_API_URL || '', // Use environment variable in production
  
  SOCKET_URL: isDevelopment
    ? '' // Use relative URLs for Vite proxy
    : import.meta.env.VITE_SOCKET_URL || '', // Use environment variable in production
  
  TIMEOUT: 10000, // 10 seconds
};

// Socket configuration
export const SOCKET_CONFIG = {
  URL: API_CONFIG.SOCKET_URL,
  OPTIONS: {
    transports: ['websocket', 'polling'],
    upgrade: true,
    rememberUpgrade: true,
    timeout: 20000,
    forceNew: false,
    reconnection: true,
    reconnectionDelay: 1000,
    reconnectionAttempts: 5,
    maxReconnectionAttempts: 5,
  }
};

// Application configuration
export const APP_CONFIG = {
  NAME: 'Sociality',
  VERSION: '1.0.0',
  ENVIRONMENT: isDevelopment ? 'development' : 'production',
  DEBUG: isDevelopment,
};

// Feature flags
export const FEATURES = {
  CROSS_PLATFORM: true,
  FEDERATION: true,
  REAL_TIME_NOTIFICATIONS: true,
  OAUTH_LOGIN: true,
};

// Export environment info
export const ENV = {
  isDevelopment,
  isProduction,
  NODE_ENV: import.meta.env.MODE,
};

// Helper function to get full API URL
export const getApiUrl = (endpoint = '') => {
  const baseUrl = API_CONFIG.BASE_URL;
  if (!baseUrl) {
    // Use relative URL
    return `/api${endpoint}`;
  }
  // Use absolute URL
  return `${baseUrl}/api${endpoint}`;
};

// Helper function to get full socket URL
export const getSocketUrl = () => {
  const socketUrl = API_CONFIG.SOCKET_URL;
  if (!socketUrl) {
    // Use relative URL (current domain)
    return window.location.origin;
  }
  return socketUrl;
};

console.log('Environment Configuration:', {
  environment: APP_CONFIG.ENVIRONMENT,
  apiBaseUrl: API_CONFIG.BASE_URL,
  socketUrl: API_CONFIG.SOCKET_URL,
  debug: APP_CONFIG.DEBUG
});
