#!/bin/bash

# Sociality Deployment Setup Script
echo "🚀 Setting up Sociality for deployment..."

# Check if we're in the right directory
if [ ! -d "sociality" ]; then
    echo "❌ Error: Please run this script from the root directory containing the 'sociality' folder"
    exit 1
fi

# Create environment files if they don't exist
echo "📝 Creating environment files..."

if [ ! -f "sociality/backend/.env" ]; then
    cp sociality/backend/.env.example sociality/backend/.env
    echo "✅ Created sociality/backend/.env from example"
    echo "⚠️  Please edit sociality/backend/.env with your actual values"
else
    echo "ℹ️  sociality/backend/.env already exists"
fi

if [ ! -f "sociality/frontend/.env" ]; then
    cp sociality/frontend/.env.example sociality/frontend/.env
    echo "✅ Created sociality/frontend/.env from example"
    echo "⚠️  Please edit sociality/frontend/.env with your actual values"
else
    echo "ℹ️  sociality/frontend/.env already exists"
fi

# Check if git is initialized
if [ ! -d ".git" ]; then
    echo "🔧 Initializing Git repository..."
    git init
    git add .
    git commit -m "Initial commit for deployment"
    echo "✅ Git repository initialized"
    echo "⚠️  Please add your GitHub remote: git remote add origin <your-repo-url>"
else
    echo "ℹ️  Git repository already exists"
fi

# Install dependencies
echo "📦 Installing dependencies..."

echo "Installing backend dependencies..."
cd sociality/backend
npm install
cd ../..

echo "Installing frontend dependencies..."
cd sociality/frontend
npm install
cd ../..

echo "✅ Dependencies installed"

# Check for required files
echo "🔍 Checking deployment files..."

required_files=(
    ".gitignore"
    "render.yaml"
    "vercel.json"
    "DEPLOYMENT.md"
    "sociality/backend/.env.example"
    "sociality/frontend/.env.example"
)

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file exists"
    else
        echo "❌ $file is missing"
    fi
done

echo ""
echo "🎉 Deployment setup complete!"
echo ""
echo "Next steps:"
echo "1. Edit your .env files with actual values"
echo "2. Push your code to GitHub"
echo "3. Follow the DEPLOYMENT.md guide"
echo "4. Deploy backend to Render"
echo "5. Deploy frontend to Vercel"
echo ""
echo "📖 Read DEPLOYMENT.md for detailed instructions"
