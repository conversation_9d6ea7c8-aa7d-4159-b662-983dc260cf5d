# 🚀 Complete Deployment Guide - Sociality Platform

This comprehensive guide covers deploying the full Sociality platform with all features including Telegram bot, Discord bot, Google OAuth, and cross-platform messaging.

## 📋 Prerequisites

### Required Accounts & Services
- [GitHub](https://github.com) - Code repository
- [Render](https://render.com) - Backend hosting
- [Vercel](https://vercel.com) - Frontend hosting
- [MongoDB Atlas](https://cloud.mongodb.com) - Database
- [Cloudinary](https://cloudinary.com) - Media storage
- [Google Cloud Console](https://console.cloud.google.com) - OAuth & APIs
- [Telegram](https://core.telegram.org/bots) - Bot API
- [Discord Developer Portal](https://discord.com/developers/applications) - Bot API

### Required API Keys & Tokens
- MongoDB connection string
- Cloudinary credentials
- Google OAuth credentials
- Telegram bot token
- Discord bot token

## 🔧 Step 1: Database Setup (MongoDB Atlas)

### 1.1 Create MongoDB Cluster
1. Go to [MongoDB Atlas](https://cloud.mongodb.com)
2. Create a new project: "Sociality"
3. Build a database (Free tier is fine)
4. Choose cloud provider and region
5. Create cluster

### 1.2 Configure Database Access
1. **Database Access** → Add new user
   - Username: `sociality-admin`
   - Password: Generate secure password
   - Role: `Atlas admin`

2. **Network Access** → Add IP Address
   - Add `0.0.0.0/0` (Allow access from anywhere)
   - Or add specific IPs for better security

### 1.3 Get Connection String
1. **Clusters** → Connect → Connect your application
2. Copy connection string: `mongodb+srv://sociality-admin:<password>@cluster0.xxxxx.mongodb.net/sociality?retryWrites=true&w=majority`
3. Replace `<password>` with your actual password

## 🖼️ Step 2: Cloudinary Setup

### 2.1 Create Cloudinary Account
1. Go to [Cloudinary](https://cloudinary.com)
2. Sign up for free account
3. Go to Dashboard

### 2.2 Get Credentials
From your dashboard, copy:
- **Cloud Name**: `your-cloud-name`
- **API Key**: `***************`
- **API Secret**: `your-api-secret`

### 2.3 Configure Upload Settings
1. **Settings** → Upload
2. Enable **Auto-backup**
3. Set **Upload preset** to "unsigned" for easier integration

## 🔐 Step 3: Google OAuth Setup

### 3.1 Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Create new project: "Sociality Platform"
3. Enable APIs:
   - Google+ API
   - Google OAuth2 API

### 3.2 Configure OAuth Consent Screen
1. **APIs & Services** → OAuth consent screen
2. Choose **External** user type
3. Fill required fields:
   - App name: "Sociality"
   - User support email: your email
   - Developer contact: your email
4. Add scopes: `email`, `profile`
5. Add test users (your email)

### 3.3 Create OAuth Credentials
1. **APIs & Services** → Credentials
2. **Create Credentials** → OAuth 2.0 Client IDs
3. Application type: **Web application**
4. Name: "Sociality Web Client"
5. Authorized redirect URIs:
   ```
   http://localhost:5000/api/auth/oauth/callback
   https://your-backend-name.onrender.com/api/auth/oauth/callback
   ```
6. Copy **Client ID** and **Client Secret**

## 🤖 Step 4: Telegram Bot Setup

### 4.1 Create Telegram Bot
1. Open Telegram and search for `@BotFather`
2. Send `/newbot`
3. Choose bot name: "Sociality Bot"
4. Choose username: "sociality_your_name_bot"
5. Copy the **Bot Token**: `123456789:ABCdefGHIjklMNOpqrsTUVwxyz`

### 4.2 Configure Bot Settings
1. Send `/setdescription` to BotFather
   - Description: "Connect your Telegram to Sociality platform"
2. Send `/setabouttext`
   - About: "Cross-platform social messaging bot"
3. Send `/setuserpic` and upload a profile picture

### 4.3 Set Webhook (After Backend Deployment)
```bash
curl -X POST "https://api.telegram.org/bot<YOUR_BOT_TOKEN>/setWebhook" \
     -H "Content-Type: application/json" \
     -d '{"url": "https://your-backend-name.onrender.com/api/telegram/webhook"}'
```

## 🎮 Step 5: Discord Bot Setup

### 5.1 Create Discord Application
1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. **New Application** → Name: "Sociality Bot"
3. Go to **Bot** section
4. **Add Bot** → Confirm

### 5.2 Configure Bot
1. **Bot** section:
   - Copy **Token**: `***************************.GhIjKl.MnOpQrStUvWxYzAbCdEfGhIjKlMnOpQrStUvWx`
   - Enable **Message Content Intent**
   - Enable **Server Members Intent**

### 5.3 Bot Permissions
1. **OAuth2** → URL Generator
2. Scopes: `bot`, `applications.commands`
3. Bot Permissions:
   - Send Messages
   - Read Message History
   - Use Slash Commands
   - Attach Files
   - Embed Links
4. Copy generated URL for bot invitation

## 🏗️ Step 6: Backend Deployment (Render)

### 6.1 Prepare Backend Environment
Create `sociality/backend/.env` with all required variables:
```env
# Server Configuration
PORT=5000
NODE_ENV=production

# Database
MONGO_URI=mongodb+srv://sociality-admin:<password>@cluster0.xxxxx.mongodb.net/sociality?retryWrites=true&w=majority

# JWT & Session
JWT_SECRET=your-super-secure-jwt-secret-key-min-32-chars
SESSION_SECRET=your-session-secret-key-min-32-chars

# Cloudinary
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=***************
CLOUDINARY_API_SECRET=your-api-secret

# Google OAuth
GOOGLE_CLIENT_ID=123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=GOCSPX-abcdefghijklmnopqrstuvwxyz123456

# Telegram Bot
TELEGRAM_BOT_TOKEN=123456789:ABCdefGHIjklMNOpqrsTUVwxyz
TELEGRAM_WEBHOOK_URL=https://your-backend-name.onrender.com/api/telegram/webhook

# Discord Bot
DISCORD_BOT_TOKEN=***************************.GhIjKl.MnOpQrStUvWxYzAbCdEfGhIjKlMnOpQrStUvWx
DISCORD_CLIENT_ID=***************678
DISCORD_CLIENT_SECRET=abcdefghijklmnopqrstuvwxyz123456

# CORS & Frontend
FRONTEND_URL=https://your-frontend-name.vercel.app
CORS_ORIGIN=https://your-frontend-name.vercel.app

# Federation
FEDERATION_ENABLED=true
FEDERATION_REGISTRY_URL=https://your-backend-name.onrender.com
PLATFORM_URL=https://your-backend-name.onrender.com
PLATFORM_NAME=sociality

# Cross-Platform
ENABLE_CROSS_PLATFORM=true
```

### 6.2 Deploy to Render
1. **Connect GitHub to Render:**
   - Go to https://render.com
   - Sign up/Login with GitHub
   - Click "New +" → "Web Service"
   - Connect your GitHub repository

2. **Configure the Web Service:**
   - **Name:** `sociality-backend`
   - **Environment:** `Node`
   - **Region:** `Oregon (US West)`
   - **Branch:** `main`
   - **Build Command:** `cd sociality/backend && npm install`
   - **Start Command:** `cd sociality/backend && npm start`
   - **Health Check Path:** `/api/health`

3. **Set Environment Variables:**
   Copy all variables from your `.env` file to Render's Environment tab

4. **Deploy:**
   - Click "Create Web Service"
   - Wait for deployment to complete
   - Note your backend URL: `https://your-backend-name.onrender.com`

## 🌐 Step 7: Frontend Deployment (Vercel)

### 7.1 Prepare Frontend Environment
Create `sociality/frontend/.env`:
```env
# API Configuration
VITE_API_URL=https://your-backend-name.onrender.com
VITE_SOCKET_URL=https://your-backend-name.onrender.com

# Environment
VITE_NODE_ENV=production
```

### 7.2 Deploy to Vercel
1. **Connect GitHub to Vercel:**
   - Go to https://vercel.com
   - Sign up/Login with GitHub
   - Click "New Project"
   - Import your GitHub repository

2. **Configure the Project:**
   - **Framework Preset:** `Vite`
   - **Root Directory:** `sociality/frontend`
   - **Build Command:** `npm run build`
   - **Output Directory:** `dist`
   - **Install Command:** `npm install`

3. **Set Environment Variables:**
   Go to Settings → Environment Variables and add:
   ```
   VITE_API_URL=https://your-backend-name.onrender.com
   VITE_SOCKET_URL=https://your-backend-name.onrender.com
   VITE_NODE_ENV=production
   ```

4. **Deploy:**
   - Click "Deploy"
   - Wait for deployment to complete
   - Note your frontend URL: `https://your-frontend-name.vercel.app`

## 🔄 Step 8: Update Cross-References

### 8.1 Update Backend Environment
1. Go to your Render backend service
2. Environment tab
3. Update these variables with your actual URLs:
   ```
   FRONTEND_URL=https://your-frontend-name.vercel.app
   CORS_ORIGIN=https://your-frontend-name.vercel.app
   TELEGRAM_WEBHOOK_URL=https://your-backend-name.onrender.com/api/telegram/webhook
   FEDERATION_REGISTRY_URL=https://your-backend-name.onrender.com
   PLATFORM_URL=https://your-backend-name.onrender.com
   ```
4. Redeploy the service

### 8.2 Update OAuth Redirects
1. **Google OAuth:**
   - Go to Google Cloud Console
   - Update authorized redirect URIs:
     ```
     https://your-backend-name.onrender.com/api/auth/oauth/callback
     ```

### 8.3 Set Telegram Webhook
```bash
curl -X POST "https://api.telegram.org/bot<YOUR_BOT_TOKEN>/setWebhook" \
     -H "Content-Type: application/json" \
     -d '{"url": "https://your-backend-name.onrender.com/api/telegram/webhook"}'
```

## 🧪 Step 9: Test Full Deployment

### 9.1 Basic Functionality
1. Visit your frontend URL: `https://your-frontend-name.vercel.app`
2. Test user registration with Google OAuth
3. Test posting functionality
4. Test real-time messaging
5. Test image uploads

### 9.2 Cross-Platform Features
1. **Telegram Integration:**
   - Add your bot to Telegram
   - Send `/start` command
   - Test message synchronization

2. **Discord Integration:**
   - Invite bot to Discord server using generated URL
   - Test slash commands
   - Test message synchronization

### 9.3 Federation Testing
1. Test federation registry endpoints
2. Verify cross-platform user discovery
3. Test federated messaging

## 🔄 Automatic Deployments

Both platforms will auto-deploy when you push to GitHub:

1. **Push changes:**
   ```bash
   git add .
   git commit -m "Your changes"
   git push origin main
   ```

2. **Monitor deployments:**
   - **Render:** Check service dashboard for backend
   - **Vercel:** Check project dashboard for frontend

## 🛠️ Troubleshooting

### Common Issues:

1. **Build Failures:**
   - Check build logs in Render/Vercel dashboards
   - Ensure all dependencies are in package.json
   - Verify environment variables

2. **CORS Errors:**
   - Verify FRONTEND_URL in backend environment
   - Check CORS configuration in server.js

3. **Database Connection:**
   - Verify MongoDB URI format
   - Check network access in MongoDB Atlas
   - Test connection string locally

4. **Bot Integration Issues:**
   - Verify bot tokens are correct
   - Check webhook URLs are accessible
   - Ensure proper bot permissions

5. **OAuth Issues:**
   - Verify redirect URIs match exactly
   - Check client ID and secret
   - Ensure OAuth consent screen is configured

### Debug Commands:

```bash
# Test backend health
curl https://your-backend-name.onrender.com/api/health

# Test Telegram webhook
curl -X POST "https://api.telegram.org/bot<TOKEN>/getWebhookInfo"

# Test Discord bot status
curl -H "Authorization: Bot <TOKEN>" https://discord.com/api/v10/users/@me
```

### Logs:
- **Render:** Service → Logs tab
- **Vercel:** Project → Functions tab → View logs
- **MongoDB:** Atlas → Database → Browse Collections

## 🔒 Security Checklist

- [ ] All `.env` files are in `.gitignore`
- [ ] Strong JWT and session secrets (32+ characters)
- [ ] MongoDB network access properly configured
- [ ] OAuth redirect URIs use HTTPS
- [ ] Bot tokens are kept secure
- [ ] CORS origins are specific (not `*`)
- [ ] Environment variables are set in production
- [ ] Regular monitoring is set up

## 📁 Final File Structure

```
├── .gitignore                    # Comprehensive gitignore
├── render.yaml                   # Render deployment config
├── vercel.json                   # Vercel deployment config
├── DEPLOYMENT.md                 # This guide
├── README.md                     # Project overview
└── sociality/
    ├── backend/
    │   ├── .env                  # Backend environment (not in git)
    │   ├── .env.example          # Environment template
    │   ├── package.json
    │   ├── server.js
    │   ├── routes/
    │   ├── models/
    │   ├── controllers/
    │   ├── services/
    │   ├── socket/
    │   └── platforms/            # Telegram/Discord integration
    ├── frontend/
    │   ├── .env                  # Frontend environment (not in git)
    │   ├── .env.example          # Environment template
    │   ├── package.json
    │   ├── vite.config.js
    │   └── src/
    └── federation-registry/      # Federation service
```

## 🎉 Success!

Your Sociality platform is now fully deployed with:
- ✅ Backend on Render with auto-deployment
- ✅ Frontend on Vercel with auto-deployment
- ✅ MongoDB Atlas database
- ✅ Cloudinary media storage
- ✅ Google OAuth authentication
- ✅ Telegram bot integration
- ✅ Discord bot integration
- ✅ Cross-platform messaging
- ✅ Federation capabilities

**URLs:**
- Frontend: `https://your-frontend-name.vercel.app`
- Backend: `https://your-backend-name.onrender.com`
- API Health: `https://your-backend-name.onrender.com/api/health`
