# Sociality Deployment Guide

This guide will help you deploy the Sociality application with the backend on Render and frontend on Vercel.

## Prerequisites

1. GitHub account
2. Render account (https://render.com)
3. Vercel account (https://vercel.com)
4. MongoDB Atlas account (for database)
5. Cloudinary account (for image storage)

## Step 1: Prepare Your Repository

1. **Push your code to GitHub:**
   ```bash
   git add .
   git commit -m "Initial deployment setup"
   git push origin main
   ```

2. **Environment Variables Setup:**
   - Copy `sociality/backend/.env.example` to `sociality/backend/.env`
   - Copy `sociality/frontend/.env.example` to `sociality/frontend/.env`
   - Fill in your actual values

## Step 2: Deploy Backend to Render

1. **Connect GitHub to Render:**
   - Go to https://render.com
   - Sign up/Login with GitHub
   - Click "New +" → "Web Service"
   - Connect your GitHub repository

2. **Configure the Web Service:**
   - **Name:** `sociality-backend`
   - **Environment:** `Node`
   - **Region:** `Oregon (US West)`
   - **Branch:** `main`
   - **Build Command:** `cd sociality/backend && npm install`
   - **Start Command:** `cd sociality/backend && npm start`

3. **Set Environment Variables:**
   Go to Environment tab and add these variables:
   ```
   NODE_ENV=production
   PORT=10000
   MONGO_URI=your_mongodb_connection_string
   JWT_SECRET=your_jwt_secret
   SESSION_SECRET=your_session_secret
   CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
   CLOUDINARY_API_KEY=your_cloudinary_api_key
   CLOUDINARY_API_SECRET=your_cloudinary_api_secret
   GOOGLE_CLIENT_ID=your_google_client_id
   GOOGLE_CLIENT_SECRET=your_google_client_secret
   TELEGRAM_BOT_TOKEN=your_telegram_bot_token
   TELEGRAM_BOT_USERNAME=your_telegram_bot_username
   DISCORD_BOT_TOKEN=your_discord_bot_token
   DISCORD_CLIENT_ID=your_discord_client_id
   FRONTEND_URL=https://your-frontend-domain.vercel.app
   FEDERATION_REGISTRY_URL=https://your-backend-domain.onrender.com
   PLATFORM_URL=https://your-backend-domain.onrender.com
   PLATFORM_NAME=sociality
   FEDERATION_ENABLED=true
   ENABLE_CROSS_PLATFORM=true
   ```

4. **Deploy:**
   - Click "Create Web Service"
   - Wait for deployment to complete
   - Note your backend URL: `https://your-backend-domain.onrender.com`

## Step 3: Deploy Frontend to Vercel

1. **Connect GitHub to Vercel:**
   - Go to https://vercel.com
   - Sign up/Login with GitHub
   - Click "New Project"
   - Import your GitHub repository

2. **Configure the Project:**
   - **Framework Preset:** `Vite`
   - **Root Directory:** `sociality/frontend`
   - **Build Command:** `npm run build`
   - **Output Directory:** `dist`
   - **Install Command:** `npm install`

3. **Set Environment Variables:**
   Go to Settings → Environment Variables and add:
   ```
   VITE_API_URL=https://your-backend-domain.onrender.com
   VITE_SOCKET_URL=https://your-backend-domain.onrender.com
   VITE_NODE_ENV=production
   ```

4. **Deploy:**
   - Click "Deploy"
   - Wait for deployment to complete
   - Note your frontend URL: `https://your-frontend-domain.vercel.app`

## Step 4: Update Backend Environment

1. **Update FRONTEND_URL in Render:**
   - Go to your Render backend service
   - Environment tab
   - Update `FRONTEND_URL` to your actual Vercel URL
   - Redeploy the service

## Step 5: Configure OAuth Redirects

1. **Google OAuth:**
   - Go to Google Cloud Console
   - Update authorized redirect URIs:
     - `https://your-backend-domain.onrender.com/api/auth/google/callback`
     - `https://your-frontend-domain.vercel.app/auth/callback`

2. **Update CORS settings if needed**

## Step 6: Test Deployment

1. Visit your frontend URL
2. Test user registration/login
3. Test posting functionality
4. Test real-time features
5. Test cross-platform integration

## Automatic Deployments

Both Render and Vercel will automatically deploy when you push to your main branch:

1. **Push changes:**
   ```bash
   git add .
   git commit -m "Your changes"
   git push origin main
   ```

2. **Monitor deployments:**
   - Render: Check your service dashboard
   - Vercel: Check your project dashboard

## Troubleshooting

### Common Issues:

1. **Build Failures:**
   - Check build logs in Render/Vercel dashboards
   - Ensure all dependencies are in package.json
   - Check environment variables

2. **CORS Errors:**
   - Verify FRONTEND_URL in backend environment
   - Check CORS configuration in backend

3. **Database Connection:**
   - Verify MongoDB URI
   - Check network access in MongoDB Atlas

4. **Environment Variables:**
   - Ensure all required variables are set
   - Check for typos in variable names

### Logs:

- **Render:** Service → Logs tab
- **Vercel:** Project → Functions tab → View logs

## File Structure for Deployment

```
├── .gitignore
├── render.yaml
├── vercel.json
├── DEPLOYMENT.md
└── sociality/
    ├── backend/
    │   ├── .env.example
    │   ├── package.json
    │   └── server.js
    └── frontend/
        ├── .env.example
        ├── package.json
        └── vite.config.js
```

## Security Notes

- Never commit `.env` files to Git
- Use strong, unique secrets for JWT and sessions
- Regularly rotate API keys and tokens
- Enable HTTPS only in production
- Monitor your application logs regularly
