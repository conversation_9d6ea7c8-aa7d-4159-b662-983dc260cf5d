# ================================
# SOCIALITY BACKEND CONFIGURATION
# ================================

# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
# Get this from MongoDB Atlas: https://cloud.mongodb.com
MONGO_URI=mongodb+srv://username:<EMAIL>/sociality?retryWrites=true&w=majority

# JWT & Session Security
# Generate strong random strings (32+ characters)
JWT_SECRET=your-super-secure-jwt-secret-key-minimum-32-characters
SESSION_SECRET=your-session-secret-key-minimum-32-characters

# Cloudinary Configuration
# Get these from: https://cloudinary.com/console
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=123456789012345
CLOUDINARY_API_SECRET=your-api-secret

# Google OAuth Configuration
# Get these from: https://console.cloud.google.com
GOOGLE_CLIENT_ID=123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=GOCSPX-abcdefghijklmnopqrstuvwxyz123456

# Telegram Bot Configuration
# Get bot token from @BotFather on Telegram
TELEGRAM_BOT_TOKEN=123456789:ABCdefGHIjklMNOpqrsTUVwxyz
TELEGRAM_WEBHOOK_URL=https://your-backend-domain.onrender.com/api/telegram/webhook

# Discord Bot Configuration
# Get these from: https://discord.com/developers/applications
DISCORD_BOT_TOKEN=MTIzNDU2Nzg5MDEyMzQ1Njc4OTA.GhIjKl.MnOpQrStUvWxYzAbCdEfGhIjKlMnOpQrStUvWx
DISCORD_CLIENT_ID=123456789012345678
DISCORD_CLIENT_SECRET=abcdefghijklmnopqrstuvwxyz123456

# CORS & Frontend Configuration
FRONTEND_URL=http://localhost:7100
CORS_ORIGIN=http://localhost:7100

# Federation Configuration
FEDERATION_ENABLED=true
FEDERATION_REGISTRY_URL=https://your-backend-domain.onrender.com
PLATFORM_URL=https://your-backend-domain.onrender.com
PLATFORM_NAME=sociality

# Cross-Platform Features
ENABLE_CROSS_PLATFORM=true

# ================================
# PRODUCTION DEPLOYMENT NOTES
# ================================
#
# For production deployment:
# 1. Replace localhost URLs with your actual domain names
# 2. Use strong, unique secrets for JWT_SECRET and SESSION_SECRET
# 3. Set NODE_ENV=production
# 4. Update FRONTEND_URL and CORS_ORIGIN to your Vercel domain
# 5. Set TELEGRAM_WEBHOOK_URL to your Render backend domain
# 6. Ensure all API keys and tokens are properly configured
#
# Security reminders:
# - Never commit this file to Git (it's in .gitignore)
# - Use environment variables in production platforms
# - Regularly rotate API keys and secrets
# - Monitor your application logs for security issues
