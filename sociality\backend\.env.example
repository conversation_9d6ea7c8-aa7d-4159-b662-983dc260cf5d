# Server Configuration
PORT=5000
NODE_ENV=production

# Database Configuration
MONGO_URI=your_mongodb_connection_string_here

# JWT Configuration
JWT_SECRET=your_jwt_secret_here
SESSION_SECRET=your_session_secret_here

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# Google OAuth2 Configuration
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Frontend URL
FRONTEND_URL=https://your-frontend-domain.vercel.app

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_BOT_USERNAME=@your_telegram_bot_username

# Discord Bot Configuration
DISCORD_BOT_TOKEN=your_discord_bot_token
DISCORD_CLIENT_ID=your_discord_client_id

# Federation Configuration
FEDERATION_REGISTRY_URL=https://your-backend-domain.onrender.com
PLATFORM_URL=https://your-backend-domain.onrender.com
PLATFORM_NAME=sociality
FEDERATION_ENABLED=true
ENABLE_CROSS_PLATFORM=true
TELEGRAM_PLATFORM_URL=https://your-backend-domain.onrender.com
DISCORD_PLATFORM_URL=https://your-backend-domain.onrender.com
