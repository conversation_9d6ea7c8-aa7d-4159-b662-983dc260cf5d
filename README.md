# Sociality - Social Media Platform

A modern, full-stack social media platform with real-time messaging, cross-platform integration, and federation capabilities.

## 🚀 Features

- **Real-time Messaging**: Instant messaging with Socket.IO
- **Cross-Platform Integration**: Connect with Telegram and Discord
- **Federation**: Connect with other Sociality instances
- **OAuth Authentication**: Google OAuth integration
- **Media Sharing**: Image and file uploads with Cloudinary
- **Responsive Design**: Mobile-first design with Chakra UI
- **Dark/Light Mode**: Theme switching support

## 🛠️ Tech Stack

### Backend
- **Node.js** with Express.js
- **MongoDB** with Mongoose
- **Socket.IO** for real-time communication
- **Cloudinary** for media storage
- **JWT** for authentication
- **Passport.js** for OAuth

### Frontend
- **React 18** with Vite
- **Chakra UI** for components
- **Recoil** for state management
- **Socket.IO Client** for real-time features
- **React Router** for navigation

## 📁 Project Structure

```
sociality/
├── backend/                 # Node.js backend
│   ├── config/             # Configuration files
│   ├── controllers/        # Route controllers
│   ├── models/            # MongoDB models
│   ├── routes/            # API routes
│   ├── services/          # Business logic
│   ├── socket/            # Socket.IO handlers
│   └── server.js          # Entry point
├── frontend/              # React frontend
│   ├── src/
│   │   ├── components/    # React components
│   │   ├── pages/         # Page components
│   │   ├── hooks/         # Custom hooks
│   │   ├── atoms/         # Recoil state
│   │   ├── utils/         # Utilities
│   │   └── config/        # Configuration
│   └── public/            # Static assets
└── federation-registry/   # Federation service
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- MongoDB
- Cloudinary account
- Google OAuth credentials

### Local Development

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd Sociality
   ```

2. **Run setup script**
   ```bash
   # On Windows
   deploy-setup.bat
   
   # On macOS/Linux
   chmod +x deploy-setup.sh
   ./deploy-setup.sh
   ```

3. **Configure environment variables**
   - Edit `sociality/backend/.env` with your values
   - Edit `sociality/frontend/.env` with your values

4. **Start the application**
   ```bash
   # Backend (Terminal 1)
   cd sociality/backend
   npm run dev
   
   # Frontend (Terminal 2)
   cd sociality/frontend
   npm run dev
   ```

5. **Access the application**
   - Frontend: http://localhost:7100
   - Backend: http://localhost:5000

## 🌐 Deployment

This project is configured for deployment on:
- **Backend**: Render.com
- **Frontend**: Vercel.com
- **Database**: MongoDB Atlas

### Automatic Deployment

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "Deploy to production"
   git push origin main
   ```

2. **Both platforms will automatically deploy** when you push to the main branch

### Manual Deployment

Follow the detailed guide in [DEPLOYMENT.md](./DEPLOYMENT.md)

## 📋 Environment Variables

### Backend (.env)
```env
PORT=5000
MONGO_URI=your_mongodb_connection_string
JWT_SECRET=your_jwt_secret
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
FRONTEND_URL=https://your-frontend-domain.vercel.app
```

### Frontend (.env)
```env
VITE_API_URL=https://your-backend-domain.onrender.com
VITE_SOCKET_URL=https://your-backend-domain.onrender.com
```

## 🔧 Development

### Available Scripts

#### Backend
```bash
npm run dev      # Development with nodemon
npm start        # Production start
```

#### Frontend
```bash
npm run dev      # Development server
npm run build    # Production build
npm run preview  # Preview production build
npm run test     # Run tests
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For deployment issues, check:
1. [DEPLOYMENT.md](./DEPLOYMENT.md) - Detailed deployment guide
2. Environment variables are correctly set
3. All services are running
4. Check logs in Render/Vercel dashboards

## 🔗 Links

- **Live Demo**: [Your deployed URL]
- **Documentation**: [DEPLOYMENT.md](./DEPLOYMENT.md)
- **Issues**: [GitHub Issues]
