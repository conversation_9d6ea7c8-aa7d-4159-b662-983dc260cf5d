import express from "express";

const router = express.Router();

// Health check endpoint for deployment platforms
router.get("/health", (req, res) => {
    res.status(200).json({
        status: "OK",
        message: "Sociality Backend is running",
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || "development",
        version: "1.0.0"
    });
});

// Readiness check endpoint
router.get("/ready", (req, res) => {
    // Add any additional checks here (database connection, etc.)
    res.status(200).json({
        status: "READY",
        message: "Sociality Backend is ready to serve requests",
        timestamp: new Date().toISOString()
    });
});

export default router;
