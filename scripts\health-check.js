#!/usr/bin/env node

/**
 * Health check script for Sociality deployment
 * Verifies that both frontend and backend are running correctly
 */

import fetch from 'node-fetch';

const COLORS = {
  GREEN: '\x1b[32m',
  RED: '\x1b[31m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  RESET: '\x1b[0m'
};

const log = (color, message) => console.log(`${color}${message}${COLORS.RESET}`);

async function checkHealth(url, name) {
  try {
    log(COLORS.BLUE, `🔍 Checking ${name}...`);
    
    const response = await fetch(url, {
      method: 'GET',
      timeout: 10000
    });
    
    if (response.ok) {
      const data = await response.json();
      log(COLORS.GREEN, `✅ ${name} is healthy`);
      console.log(`   Status: ${data.status || 'OK'}`);
      console.log(`   Timestamp: ${data.timestamp || new Date().toISOString()}`);
      return true;
    } else {
      log(COLORS.RED, `❌ ${name} returned status ${response.status}`);
      return false;
    }
  } catch (error) {
    log(COLORS.RED, `❌ ${name} health check failed: ${error.message}`);
    return false;
  }
}

async function checkWebsite(url, name) {
  try {
    log(COLORS.BLUE, `🔍 Checking ${name} website...`);
    
    const response = await fetch(url, {
      method: 'GET',
      timeout: 10000
    });
    
    if (response.ok) {
      log(COLORS.GREEN, `✅ ${name} website is accessible`);
      return true;
    } else {
      log(COLORS.RED, `❌ ${name} website returned status ${response.status}`);
      return false;
    }
  } catch (error) {
    log(COLORS.RED, `❌ ${name} website check failed: ${error.message}`);
    return false;
  }
}

async function main() {
  log(COLORS.BLUE, '🚀 Starting Sociality Health Check...\n');
  
  // Get URLs from command line arguments or use defaults
  const backendUrl = process.argv[2] || 'http://localhost:5000';
  const frontendUrl = process.argv[3] || 'http://localhost:7100';
  
  console.log(`Backend URL: ${backendUrl}`);
  console.log(`Frontend URL: ${frontendUrl}\n`);
  
  const results = [];
  
  // Check backend health endpoint
  results.push(await checkHealth(`${backendUrl}/api/health`, 'Backend Health'));
  
  // Check backend ready endpoint
  results.push(await checkHealth(`${backendUrl}/api/ready`, 'Backend Ready'));
  
  // Check frontend website
  results.push(await checkWebsite(frontendUrl, 'Frontend'));
  
  // Summary
  const passed = results.filter(r => r).length;
  const total = results.length;
  
  console.log('\n' + '='.repeat(50));
  
  if (passed === total) {
    log(COLORS.GREEN, `🎉 All checks passed! (${passed}/${total})`);
    log(COLORS.GREEN, '✅ Sociality is running correctly');
    process.exit(0);
  } else {
    log(COLORS.YELLOW, `⚠️  Some checks failed (${passed}/${total})`);
    log(COLORS.RED, '❌ Please check the logs above for issues');
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  log(COLORS.RED, `❌ Unhandled Rejection at: ${promise}, reason: ${reason}`);
  process.exit(1);
});

main().catch(error => {
  log(COLORS.RED, `❌ Health check failed: ${error.message}`);
  process.exit(1);
});
