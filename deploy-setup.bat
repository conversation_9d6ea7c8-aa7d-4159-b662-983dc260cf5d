@echo off
echo 🚀 Setting up Sociality for deployment...

REM Check if we're in the right directory
if not exist "sociality" (
    echo ❌ Error: Please run this script from the root directory containing the 'sociality' folder
    pause
    exit /b 1
)

REM Create environment files if they don't exist
echo 📝 Creating environment files...

if not exist "sociality\backend\.env" (
    copy "sociality\backend\.env.example" "sociality\backend\.env"
    echo ✅ Created sociality\backend\.env from example
    echo ⚠️  Please edit sociality\backend\.env with your actual values
) else (
    echo ℹ️  sociality\backend\.env already exists
)

if not exist "sociality\frontend\.env" (
    copy "sociality\frontend\.env.example" "sociality\frontend\.env"
    echo ✅ Created sociality\frontend\.env from example
    echo ⚠️  Please edit sociality\frontend\.env with your actual values
) else (
    echo ℹ️  sociality\frontend\.env already exists
)

REM Check if git is initialized
if not exist ".git" (
    echo 🔧 Initializing Git repository...
    git init
    git add .
    git commit -m "Initial commit for deployment"
    echo ✅ Git repository initialized
    echo ⚠️  Please add your GitHub remote: git remote add origin ^<your-repo-url^>
) else (
    echo ℹ️  Git repository already exists
)

REM Install dependencies
echo 📦 Installing dependencies...

echo Installing backend dependencies...
cd sociality\backend
call npm install
cd ..\..

echo Installing frontend dependencies...
cd sociality\frontend
call npm install
cd ..\..

echo ✅ Dependencies installed

REM Check for required files
echo 🔍 Checking deployment files...

set "files=.gitignore render.yaml vercel.json DEPLOYMENT.md sociality\backend\.env.example sociality\frontend\.env.example"

for %%f in (%files%) do (
    if exist "%%f" (
        echo ✅ %%f exists
    ) else (
        echo ❌ %%f is missing
    )
)

echo.
echo 🎉 Deployment setup complete!
echo.
echo Next steps:
echo 1. Edit your .env files with actual values
echo 2. Push your code to GitHub
echo 3. Follow the DEPLOYMENT.md guide
echo 4. Deploy backend to Render
echo 5. Deploy frontend to Vercel
echo.
echo 📖 Read DEPLOYMENT.md for detailed instructions
pause
